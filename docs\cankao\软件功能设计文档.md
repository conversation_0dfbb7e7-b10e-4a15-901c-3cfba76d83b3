# 路面生命周期碳排放计算管理平台 - 软件功能设计文档

## 1. 系统概述

路面生命周期碳排放计算管理平台是一个专门用于道路铺面全生命周期碳排放计算、管理和分析的Web应用系统。系统采用前后端分离架构，前端基于Vue.js，后端基于Odoo框架，为用户提供从项目创建到碳排放计算结果分析的完整解决方案。

## 2. 整体功能模块设计图

### 2.1 系统总体架构

```mermaid
graph TB
    subgraph "前端模块 (Vue.js)"
        A[用户认证模块] --> B[首页概览模块]
        A --> C[项目管理模块]
        A --> D[数据管理模块]
        A --> E[用户管理模块]
        
        B --> B1[项目统计展示]
        B --> B2[碳排放排行]
        B --> B3[地图可视化]
        
        C --> C1[项目列表管理]
        C --> C2[项目详情配置]
        C --> C3[计算结果展示]
        
        D --> D1[生命周期清单管理]
        D --> D2[清单详情配置]
        
        E --> E1[用户信息管理]
        E --> E2[子账号管理]
    end
    
    subgraph "后端模块 (Odoo)"
        F[API控制器] --> G[业务服务层]
        G --> H[数据模型层]
        H --> I[数据库层]
        
        G --> G1[项目服务]
        G --> G2[清单服务]
        G --> G3[计算服务]
        G --> G4[报表服务]
        
        H --> H1[项目模型]
        H --> H2[清单模型]
        H --> H3[结果模型]
        H --> H4[用户模型]
    end
    
    A -.->|RESTful API| F
    B -.->|数据请求| F
    C -.->|CRUD操作| F
    D -.->|数据管理| F
    E -.->|用户操作| F
```

### 2.2 核心功能模块关系图

```mermaid
graph LR
    subgraph "核心业务流程"
        A[用户登录] --> B[创建项目]
        B --> C[配置项目参数]
        C --> D[选择计算模式]
        D --> E[粗算模式]
        D --> F[精算模式]
        E --> G[生成计算结果]
        F --> G
        G --> H[结果分析展示]
        H --> I[报表生成]
    end
    
    subgraph "支撑功能"
        J[生命周期清单管理]
        K[基础数据管理]
        L[用户权限管理]
    end
    
    C -.-> J
    C -.-> K
    A -.-> L
```

## 3. 功能模块详细说明

### 3.1 用户认证模块

**主要功能：**
- 用户登录（支持密码登录和短信验证码登录）
- 用户注册
- 会话管理
- 权限验证

**技术实现：**
- 前端：Vue Router路由守卫、Vuex状态管理
- 后端：Session-based认证、用户角色权限控制

**核心组件：**
- `LoginView.vue` - 登录页面
- `RegisterView.vue` - 注册页面
- 阿里云验证码组件集成

### 3.2 首页概览模块

**主要功能：**
- 项目数量统计展示
- 已完成计算项目统计
- 碳排放排行榜（支持多种排行类型）
- 中国地图可视化展示项目分布
- 主子账号切换功能

**技术实现：**
- ECharts图表展示
- Leaflet地图组件
- 实时数据更新

**核心API：**
- `/users/projects/overview` - 获取项目概览数据
- `/users/projects/ranking` - 获取排行数据
- `/geojson` - 获取地理数据

### 3.3 项目管理模块

**主要功能：**
- 项目列表展示（支持分页、搜索）
- 项目创建和编辑
- 项目详情配置
- 计算方案管理（粗算/精算）
- 计算结果展示和分析
- 报表生成和下载

**核心子模块：**

#### 3.3.1 项目列表管理
- 项目基本信息展示
- 项目状态管理
- 批量操作支持

#### 3.3.2 项目详情配置
- 项目基本参数设置（名称、地点、面积、使用年限）
- 计算阶段选择（原材料、运输、施工、养护、拆除、碳汇）
- 结构层配置
- 数据填报

#### 3.3.3 计算结果展示
- 多维度结果展示（总碳排放、单位面积强度、年均强度等）
- 图表可视化分析
- 结果对比功能

**数据模型：**
- `carbon_project` - 项目主表
- `carbon_project_scheme` - 项目方案表
- `carbon_project_result` - 计算结果表

### 3.4 数据管理模块

**主要功能：**
- 生命周期清单管理
- 清单详情配置
- 基础数据维护

**核心子模块：**

#### 3.4.1 生命周期清单管理
- 清单创建、编辑、删除
- 清单激活状态管理
- 清单搜索和分页

#### 3.4.2 清单详情配置
- 材料清单配置
- 机械清单配置
- 养护清单配置
- 碳汇清单配置
- 碳排放因子设置

**数据模型：**
- `life_cycle_inventory` - 生命周期清单主表
- `material_life_cycle_inventory` - 材料清单
- `mechanical_life_cycle_inventory` - 机械清单
- `maintenance_life_cycle_inventory` - 养护清单
- `carbon_life_cycle_inventory` - 碳汇清单

### 3.5 用户管理模块

**主要功能：**
- 用户信息管理
- 主子账号体系
- 权限分配
- 用户操作日志

**权限体系：**
- 主账号：完整功能权限
- 子账号：受限功能权限
- 数据隔离：用户只能访问自己的数据

### 3.6 计算引擎模块

**主要功能：**
- 粗算模式计算
- 精算模式计算
- 多阶段碳排放计算
- 结果汇总和分析

**计算阶段：**
1. 原材料阶段
2. 运输阶段
3. 施工阶段
4. 养护阶段
5. 拆除阶段
6. 碳汇阶段

**计算公式：**
- 总碳排放 = Σ(各阶段碳排放量)
- 单位面积碳排放强度 = 总碳排放 / 铺装面积
- 年均碳排放强度 = 总碳排放 / 使用年限
- 单位面积年均碳排放强度 = 单位面积碳排放强度 / 使用年限

### 3.7 报表生成模块

**主要功能：**
- PDF报表生成
- 报表模板管理
- 报表下载
- 报表对比

**报表类型：**
- 粗算报表
- 精算报表
- 对比报表

### 3.8 基础数据模块

**主要功能：**
- 行政区划管理（省市区）
- 结构层管理
- 单位管理
- 阶段管理

**数据维护：**
- 支持基础数据的增删改查
- 数据关联关系维护
- 数据有效性验证

## 4. 技术架构特点

### 4.1 前端架构
- **模块化设计**：按功能模块组织代码结构
- **组件化开发**：可复用的UI组件
- **状态管理**：Vuex集中管理应用状态
- **路由管理**：Vue Router实现单页面应用

### 4.2 后端架构
- **RESTful API**：标准化的API接口设计
- **服务层分离**：业务逻辑与数据访问分离
- **模型驱动**：基于Odoo ORM的数据模型
- **权限控制**：基于角色的访问控制

### 4.3 数据流转
- **前后端分离**：通过HTTP API进行数据交互
- **数据验证**：前后端双重数据验证
- **错误处理**：统一的错误处理机制
- **缓存机制**：提高系统响应性能

## 5. 系统特色功能

### 5.1 双模式计算
- **粗算模式**：快速估算，适用于初步评估
- **精算模式**：详细计算，适用于精确分析

### 5.2 可视化展示
- **地图展示**：项目地理分布可视化
- **图表分析**：多维度数据图表展示
- **排行榜**：碳排放数据排名对比

### 5.3 多租户支持
- **主子账号**：支持企业级多用户管理
- **数据隔离**：确保用户数据安全
- **权限分级**：灵活的权限管理体系

### 5.4 报表系统
- **自动生成**：基于计算结果自动生成报表
- **模板化**：支持多种报表模板
- **导出功能**：支持PDF格式导出

## 6. 数据流程设计

### 6.1 项目创建与计算流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端API
    participant D as 数据库

    U->>F: 创建项目
    F->>B: POST /users/projects
    B->>D: 保存项目基本信息
    D-->>B: 返回项目ID
    B-->>F: 返回创建结果

    U->>F: 配置项目参数
    F->>B: PUT /users/projects/{id}
    B->>D: 更新项目配置

    U->>F: 选择计算模式
    F->>B: POST /users/projects/{id}/schemes
    B->>D: 创建计算方案

    U->>F: 填报数据
    F->>B: PUT /users/projects/{id}/data
    B->>B: 执行碳排放计算
    B->>D: 保存计算结果
    B-->>F: 返回计算结果
    F-->>U: 展示结果
```

### 6.2 清单管理流程

```mermaid
flowchart TD
    A[创建生命周期清单] --> B[配置清单基本信息]
    B --> C[添加材料清单项]
    C --> D[添加机械清单项]
    D --> E[添加养护清单项]
    E --> F[添加碳汇清单项]
    F --> G[设置清单为激活状态]
    G --> H[清单可用于项目计算]

    I[编辑清单] --> J[修改清单项]
    J --> K[更新碳排放因子]
    K --> L[保存修改]

    M[删除清单] --> N{检查是否被项目使用}
    N -->|是| O[提示无法删除]
    N -->|否| P[执行删除操作]
```

## 7. API接口设计

### 7.1 用户认证接口

| 接口路径 | 方法 | 功能描述 | 参数 |
|---------|------|----------|------|
| `/users/login` | POST | 用户登录 | username, password 或 phone, verifycode |
| `/users/register` | POST | 用户注册 | username, password, phone, verifycode |
| `/users/roles` | GET | 获取用户角色 | - |

### 7.2 项目管理接口

| 接口路径 | 方法 | 功能描述 | 参数 |
|---------|------|----------|------|
| `/users/projects` | GET | 获取项目列表 | keyword, curPage, pageSize |
| `/users/projects` | POST | 创建项目 | name, city_id, life, area, type, mode |
| `/users/projects/{id}` | GET | 获取项目详情 | - |
| `/users/projects/{id}` | PUT | 更新项目 | 项目参数 |
| `/users/projects/{id}` | DELETE | 删除项目 | - |
| `/users/projects/{id}/result` | GET | 获取计算结果 | - |
| `/users/projects/{id}/schemes` | POST | 创建计算方案 | mode, stages |

### 7.3 清单管理接口

| 接口路径 | 方法 | 功能描述 | 参数 |
|---------|------|----------|------|
| `/users/inventories` | GET | 获取清单列表 | keyword, curPage, pageSize |
| `/users/inventories` | POST | 创建清单 | name, description |
| `/users/inventories/{id}` | GET | 获取清单详情 | - |
| `/users/inventories/{id}` | PUT | 更新清单 | 清单参数 |
| `/users/inventories/{id}` | DELETE | 删除清单 | - |
| `/users/inventories/{id}/{type}` | GET | 获取清单项 | type: material/mechanical/maintenance/carbon |

### 7.4 基础数据接口

| 接口路径 | 方法 | 功能描述 | 参数 |
|---------|------|----------|------|
| `/citys` | GET | 获取城市列表 | - |
| `/compositions` | GET | 获取结构层成分 | - |
| `/layers` | GET | 获取结构层 | - |
| `/layers/{id}/compositions` | GET | 获取结构层成分详情 | - |
| `/geojson` | GET | 获取地理数据 | adcode, type |

## 8. 数据库设计

### 8.1 核心数据表关系

```mermaid
erDiagram
    carbon_project ||--o{ carbon_project_scheme : has
    carbon_project ||--o{ carbon_project_result : generates
    carbon_project }o--|| life_cycle_inventory : uses
    carbon_project }o--|| res_users : belongs_to

    carbon_project_scheme ||--o{ carbon_project_result : produces

    life_cycle_inventory ||--o{ material_life_cycle_inventory : contains
    life_cycle_inventory ||--o{ mechanical_life_cycle_inventory : contains
    life_cycle_inventory ||--o{ maintenance_life_cycle_inventory : contains
    life_cycle_inventory ||--o{ carbon_life_cycle_inventory : contains

    carbon_project {
        int id PK
        string name
        int city_id FK
        float life
        float area
        string type
        string mode
        int user_id FK
        text data
        boolean is_completed
    }

    carbon_project_scheme {
        int id PK
        int project_id FK
        string name
        string mode
        boolean select
        boolean is_completed
    }

    carbon_project_result {
        int id PK
        int project_id FK
        int scheme_id FK
        int stage_id FK
        float result
    }

    life_cycle_inventory {
        int id PK
        string name
        text description
        boolean is_active
        int user_id FK
    }
```

## 9. 系统部署架构

### 9.1 部署架构图

```mermaid
graph TB
    subgraph "用户层"
        A[Web浏览器]
        B[移动端浏览器]
    end

    subgraph "负载均衡层"
        C[Nginx负载均衡器]
    end

    subgraph "应用层"
        D[Vue.js前端应用]
        E[Odoo后端应用]
    end

    subgraph "数据层"
        F[PostgreSQL数据库]
        G[Redis缓存]
    end

    subgraph "文件存储"
        H[文件系统/对象存储]
    end

    A --> C
    B --> C
    C --> D
    C --> E
    E --> F
    E --> G
    E --> H
```

### 9.2 技术栈总结

**前端技术栈：**
- Vue.js 2.6.14 + Vue Router + Vuex
- Element UI 2.15.14
- ECharts 4.9.0 + Leaflet 1.7.1
- Axios 1.6.1

**后端技术栈：**
- Odoo 12 + Python
- PostgreSQL数据库
- uWSGI ********
- base_rest API框架

**部署技术：**
- Docker容器化
- Nginx反向代理
- Redis缓存

这个软件功能设计文档全面展示了路面生命周期碳排放计算管理平台的功能架构、模块设计、数据流程和技术实现，为系统的开发、维护和扩展提供了清晰的指导。
