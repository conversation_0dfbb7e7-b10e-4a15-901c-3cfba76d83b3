# 路面生命周期碳排放计算管理平台 - 后端架构分析文档

## 1. 项目概述

路面生命周期碳排放计算管理平台后端基于Odoo 12框架构建，采用模块化设计，提供碳排放计算、项目管理、清单管理等核心功能。系统采用MVC架构模式，支持RESTful API接口，使用PostgreSQL作为数据存储。

## 2. 技术栈

- **框架**: Odoo 12 (Python)
- **数据库**: PostgreSQL 
- **API**: RESTful API (base_rest插件)
- **ORM**: Odoo ORM
- **部署**: Docker容器化
- **Web服务器**: 内置Werkzeug WSGI服务器
- **权限管理**: Odoo权限系统 + security_user_roles

## 3. 后端总体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Web前端应用]
        B[移动端应用]
    end
    
    subgraph "接口层 (API Layer)"
        C[RESTful API接口]
        D[认证中间件]
        E[CORS处理]
        F[数据验证]
    end
    
    subgraph "控制器层 (Controller Layer)"
        G[CarbonController]
        H[报表控制器]
        I[文件上传控制器]
    end
    
    subgraph "服务层 (Service Layer)"
        J[CarbonProjectServices]
        K[计算服务]
        L[报表服务]
        M[用户管理服务]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        N[项目管理模型]
        O[清单管理模型]
        P[计算结果模型]
        Q[用户权限模型]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        R[Odoo ORM]
        S[模型定义]
        T[关系映射]
    end
    
    subgraph "数据存储层 (Data Storage Layer)"
        U[(PostgreSQL数据库)]
        V[项目数据表]
        W[清单数据表]
        X[计算结果表]
        Y[用户权限表]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    G --> J
    H --> L
    I --> M
    J --> N
    K --> O
    L --> P
    M --> Q
    N --> R
    O --> R
    P --> R
    Q --> R
    R --> S
    S --> T
    T --> U
    U --> V
    U --> W
    U --> X
    U --> Y
```

## 4. 框架设计分析

### 4.1 Odoo MVC架构模式

系统基于Odoo框架的MVC（Model-View-Controller）架构模式：

```mermaid
graph LR
    subgraph "MVC架构"
        A[Model 数据模型] 
        B[View 视图层]
        C[Controller 控制器]
    end
    
    subgraph "Odoo实现"
        D[Python模型类<br/>继承models.Model]
        E[XML视图定义<br/>QWeb模板]
        F[HTTP控制器<br/>RESTful API]
    end
    
    A --> D
    B --> E  
    C --> F
    
    D --> G[ORM数据操作]
    E --> H[前端界面渲染]
    F --> I[HTTP请求处理]
```

### 4.2 组件化架构

采用Odoo的组件化架构设计：

- **模块化**: 每个功能模块独立开发和部署
- **插件化**: 通过addons机制扩展功能
- **依赖管理**: 通过__manifest__.py声明模块依赖
- **服务注册**: 使用Component系统注册服务

## 5. 核心模块组成

### 5.1 主要业务模块

| 模块名称 | 路径 | 功能描述 |
|---------|------|----------|
| carbon | bimsrc/carbon | 碳排放计算核心模块 |
| base_rest | addons/api/base_rest | RESTful API基础支持 |
| base_rest_datamodel | addons/api/base_rest_datamodel | API数据模型验证 |
| security_user_roles | addons/foundation/security_user_roles | 用户角色权限管理 |
| connector | addons/connector | 组件连接器 |

### 5.2 Carbon模块详细结构

```mermaid
graph TB
    subgraph "Carbon模块结构"
        A[controllers/] --> A1[controllers.py<br/>HTTP控制器]
        B[models/] --> B1[carbon_project.py<br/>项目模型]
        B --> B2[carbon_project_scheme.py<br/>方案模型]
        B --> B3[life_cycle_inventory.py<br/>清单模型]
        B --> B4[carbon_project_result.py<br/>结果模型]
        C[services/] --> C1[model_services.py<br/>业务服务]
        D[datamodels/] --> D1[project_info.py<br/>数据模型定义]
        E[views/] --> E1[XML视图定义]
        F[security/] --> F1[权限配置]
        G[reports/] --> G1[报表模板]
    end
```

## 6. 模块间接口交互

### 6.1 API接口交互流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant API as RESTful API
    participant Auth as 认证中间件
    participant Ctrl as 控制器
    participant Svc as 服务层
    participant Model as 模型层
    participant DB as 数据库
    
    C->>API: HTTP请求
    API->>Auth: 身份验证
    Auth->>Ctrl: 路由到控制器
    Ctrl->>Svc: 调用业务服务
    Svc->>Model: ORM数据操作
    Model->>DB: SQL查询
    DB-->>Model: 返回数据
    Model-->>Svc: 返回模型对象
    Svc-->>Ctrl: 返回业务结果
    Ctrl-->>API: JSON响应
    API-->>C: HTTP响应
```

### 6.2 服务层交互机制

- **服务注册**: 通过Component装饰器注册服务
- **依赖注入**: 使用Odoo的环境(env)机制
- **事务管理**: 自动事务管理和回滚
- **缓存机制**: 模型级别的缓存支持

### 6.3 数据模型关系

```mermaid
erDiagram
    CARBON_PROJECT ||--o{ CARBON_PROJECT_SCHEME : has
    CARBON_PROJECT ||--o{ CARBON_PROJECT_RESULT : generates
    CARBON_PROJECT }o--|| LIFE_CYCLE_INVENTORY : uses
    CARBON_PROJECT }o--|| RES_COUNTRY_STATE_CITY : located_in
    CARBON_PROJECT_SCHEME ||--o{ CARBON_PROJECT_RESULT : produces
    LIFE_CYCLE_INVENTORY ||--o{ MATERIAL_LIFE_CYCLE_INVENTORY : contains
    LIFE_CYCLE_INVENTORY ||--o{ MECHANICAL_LIFE_CYCLE_INVENTORY : contains
    LIFE_CYCLE_INVENTORY ||--o{ MAINTENANCE_LIFE_CYCLE_INVENTORY : contains
```

## 7. 权限与安全架构

### 7.1 权限控制层次

```mermaid
graph TB
    A[用户认证] --> B[会话管理]
    B --> C[角色权限]
    C --> D[模型访问控制]
    D --> E[记录级权限]
    E --> F[字段级权限]
    
    G[security_user_roles] --> C
    H[ir.model.access] --> D
    I[ir.rule] --> E
    J[字段属性] --> F
```

### 7.2 API认证机制

- **Session认证**: 基于X-Openerp-Session-Id头部
- **用户权限**: 基于Odoo用户组和角色
- **接口权限**: 通过@restapi.method装饰器控制
- **数据权限**: 通过domain过滤器限制数据访问

## 8. 部署架构

### 8.1 Docker容器化部署

```mermaid
graph TB
    subgraph "Docker部署架构"
        A[Nginx反向代理] --> B[Odoo应用容器]
        B --> C[PostgreSQL数据库容器]
        B --> D[Redis缓存容器]
        E[文件存储卷] --> B
        F[数据库存储卷] --> C
    end
```

### 8.2 环境配置

- **开发环境**: 本地Python虚拟环境 + PostgreSQL
- **生产环境**: Docker Compose多容器部署
- **配置管理**: 通过环境变量和配置文件
- **日志管理**: 结构化日志输出到文件

## 9. 性能优化策略

### 9.1 数据库优化
- 索引优化
- 查询优化
- 连接池管理

### 9.2 缓存策略
- ORM级别缓存
- Redis分布式缓存
- 静态资源缓存

### 9.3 并发处理
- 多进程工作模式
- 异步任务处理
- 负载均衡支持

## 10. 扩展性设计

### 10.1 模块扩展
- 插件化架构支持新模块开发
- 继承机制支持功能扩展
- 钩子机制支持自定义逻辑

### 10.2 API扩展
- RESTful API标准化接口
- 版本控制支持
- 自定义端点扩展

### 10.3 数据扩展
- 自定义字段支持
- 关系模型扩展
- 计算字段定义

## 11. API接口详细说明

### 11.1 主要API端点

| 功能模块 | API端点 | HTTP方法 | 描述 |
|---------|---------|----------|------|
| 用户管理 | `/api/carbon_project/users/info` | GET/POST | 获取/更新用户信息 |
| 项目管理 | `/api/carbon_project/users/projects` | GET/POST | 项目列表/创建项目 |
| 项目详情 | `/api/carbon_project/users/projects/{id}` | GET/PUT/DELETE | 项目详情操作 |
| 计算结果 | `/api/carbon_project/users/projects/{id}/result` | GET | 获取计算结果 |
| 清单管理 | `/api/carbon_project/users/inventories` | GET/POST | 清单列表/创建清单 |
| 方案管理 | `/api/carbon_project/users/projects/{id}/schemes` | GET/POST | 方案列表/创建方案 |
| 报表生成 | `/api/carbon_project/reports/{type}` | GET | 生成各类报表 |

### 11.2 数据模型验证

使用base_rest_datamodel进行API数据验证：

```python
# 请求参数验证
class CarbonProjectUsersProjectsParam(Datamodel):
    _name = "carbon.project.users.projects.param"

    keyword = fields.String(description='搜索关键词')
    curPage = fields.Integer(description='当前页')
    pageSize = fields.Integer(description='每页数量')

# 响应数据验证
class CarbonProjectUsersProjectsResponse(Datamodel):
    _name = "carbon.project.users.projects.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True)
    data = fields.NestedModel('carbon.project.users.projects.data')
```

### 11.3 认证与授权

- **认证方式**: Session-based认证
- **会话管理**: X-Openerp-Session-Id请求头
- **权限控制**: 基于用户角色和组的权限系统
- **API安全**: CORS配置和CSRF保护

## 12. 数据库设计

### 12.1 核心数据表

| 表名 | 描述 | 主要字段 |
|------|------|----------|
| carbon_project | 碳排放项目 | name, city_id, life, area, type, mode |
| carbon_project_scheme | 项目方案 | project_id, name, mode, select |
| carbon_project_result | 计算结果 | project_id, scheme_id, stage_id, result |
| life_cycle_inventory | 生命周期清单 | name, type, is_active |
| material_life_cycle_inventory | 材料清单 | inventory_id, name, unit, carbon_factor |
| mechanical_life_cycle_inventory | 机械清单 | inventory_id, name, unit, carbon_factor |
| maintenance_life_cycle_inventory | 养护清单 | inventory_id, name, unit, carbon_factor |

### 12.2 关系设计

- **一对多关系**: 项目→方案、项目→结果、清单→子清单
- **多对多关系**: 项目→阶段、用户→角色
- **外键约束**: 确保数据完整性
- **索引优化**: 查询性能优化

## 13. 监控与运维

### 13.1 日志管理
- **应用日志**: Python logging模块
- **访问日志**: Nginx访问日志
- **错误日志**: 异常堆栈跟踪
- **性能日志**: 慢查询监控

### 13.2 监控指标
- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 请求响应时间、错误率
- **数据库指标**: 连接数、查询性能
- **业务指标**: 项目创建数、计算完成率

### 13.3 备份策略
- **数据库备份**: 定期PostgreSQL备份
- **文件备份**: 上传文件和报表备份
- **配置备份**: 系统配置文件备份
- **恢复测试**: 定期恢复测试验证

---

*文档版本: 1.0*
*创建日期: 2025-01-24*
*作者: AI助手*
