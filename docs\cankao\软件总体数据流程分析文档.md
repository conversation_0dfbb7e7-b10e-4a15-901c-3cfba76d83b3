# 路面生命周期碳排放计算管理平台 - 软件总体数据流程分析文档

## 1. 项目概述

路面生命周期碳排放计算管理平台是一个基于Vue.js + Odoo的前后端分离架构系统，专门用于道路铺面全生命周期碳排放的计算、管理和分析。系统通过RESTful API实现前后端数据交互，采用模块化设计支持多种计算模式和数据可视化展示。

## 2. 总体数据流程图

```mermaid
graph TB
    subgraph "前端层 (Frontend Layer)"
        A[Vue.js应用] --> B[Vuex状态管理]
        A --> C[Vue Router路由]
        A --> D[Element UI组件]
        A --> E[ECharts图表]
        A --> F[Leaflet地图]
        B --> G[Axios HTTP客户端]
    end
    
    subgraph "网络层 (Network Layer)"
        G --> H[代理服务器<br/>vue.config.js]
        H --> I[CORS处理]
        I --> J[请求转发<br/>/bimclient/*]
    end
    
    subgraph "后端API层 (Backend API Layer)"
        J --> K[RESTful API<br/>base_rest框架]
        K --> L[认证中间件<br/>Session验证]
        L --> M[数据验证<br/>Datamodel]
        M --> N[路由控制器<br/>RestController]
    end
    
    subgraph "业务服务层 (Business Service Layer)"
        N --> O[项目管理服务<br/>ProjectService]
        N --> P[清单管理服务<br/>InventoryService]
        N --> Q[计算引擎服务<br/>CalculationService]
        N --> R[用户管理服务<br/>UserService]
        N --> S[报表生成服务<br/>ReportService]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        O --> T[Odoo ORM]
        P --> T
        Q --> T
        R --> T
        S --> T
        T --> U[模型定义<br/>Models]
        U --> V[数据库操作<br/>CRUD]
    end
    
    subgraph "数据存储层 (Data Storage Layer)"
        V --> W[(PostgreSQL数据库)]
        W --> X[项目数据表<br/>carbon_project]
        W --> Y[清单数据表<br/>life_cycle_inventory]
        W --> Z[计算结果表<br/>carbon_project_result]
        W --> AA[用户数据表<br/>res_users]
    end
    
    style A fill:#e1f5fe
    style K fill:#f3e5f5
    style T fill:#e8f5e8
    style W fill:#fff3e0
```

## 3. 技术实现分析

### 3.1 前端技术栈

| 技术组件 | 版本 | 作用 | 数据处理方式 |
|---------|------|------|-------------|
| Vue.js | 2.6.14 | 核心框架 | 响应式数据绑定、组件化开发 |
| Vuex | 3.6.2 | 状态管理 | 集中式状态存储、模块化管理 |
| Vue Router | 3.5.1 | 路由管理 | SPA路由控制、权限守卫 |
| Element UI | 2.15.14 | UI组件库 | 表单验证、数据展示组件 |
| ECharts | 4.9.0 | 图表库 | 数据可视化、图表渲染 |
| Leaflet | 1.7.1 | 地图组件 | 地理数据展示、交互操作 |
| Axios | 1.6.1 | HTTP客户端 | API请求封装、拦截器处理 |

### 3.2 后端技术栈

| 技术组件 | 版本 | 作用 | 数据处理方式 |
|---------|------|------|-------------|
| Odoo | 12 | 应用框架 | MVC架构、模块化开发 |
| Python | 3.x | 编程语言 | 业务逻辑实现、数据计算 |
| PostgreSQL | - | 数据库 | 关系型数据存储、事务处理 |
| base_rest | - | API框架 | RESTful接口、数据模型验证 |
| base_rest_datamodel | - | 数据验证 | 请求参数验证、响应格式化 |
| uWSGI | ******** | Web服务器 | HTTP请求处理、进程管理 |

## 4. 核心数据流程详解

### 4.1 用户认证数据流

```mermaid
sequenceDiagram
    participant U as 用户浏览器
    participant V as Vue应用
    participant A as Axios客户端
    participant P as 代理服务器
    participant O as Odoo API
    participant D as PostgreSQL
    
    U->>V: 输入登录信息
    V->>A: 调用登录API
    A->>P: POST /bimclient/api/carbon/v2/carbon_project/users/login
    P->>O: 转发登录请求
    O->>D: 验证用户凭据
    D-->>O: 返回用户信息
    O-->>P: 返回Session ID
    P-->>A: 转发响应
    A->>A: 存储Session到localStorage
    A-->>V: 返回登录结果
    V-->>U: 更新UI状态
    
    Note over A: 后续请求自动添加<br/>X-Openerp-Session-Id头部
```

### 4.2 碳排放计算数据流

```mermaid
flowchart TD
    A[项目数据输入] --> B{选择计算模式}
    B -->|粗算模式| C[方案比选计算]
    B -->|精算模式| D[碳排放核算]
    
    C --> E[加载项目基础数据]
    D --> E
    
    E --> F[获取生命周期清单]
    F --> G[结构层配置查询]
    G --> H[材料清单数据]
    G --> I[机械清单数据]
    G --> J[养护清单数据]
    G --> K[碳汇清单数据]
    
    H --> L[原材料碳排放计算]
    I --> M[运输机械碳排放计算]
    I --> N[施工机械碳排放计算]
    J --> O[养护阶段碳排放计算]
    K --> P[碳汇效应计算]
    
    L --> Q[阶段性结果汇总]
    M --> Q
    N --> Q
    O --> Q
    P --> Q
    
    Q --> R[多维度指标生成]
    R --> S[计算结果存储]
    S --> T[前端数据展示]
    
    style A fill:#e3f2fd
    style Q fill:#f1f8e9
    style T fill:#fce4ec
```

## 5. 模块间数据交互接口

### 5.1 前端API接口封装

前端通过统一的API接口与后端交互，主要接口包括：

| 模块 | API端点 | HTTP方法 | 数据格式 | 功能描述 |
|------|---------|----------|----------|----------|
| 用户管理 | `/api/carbon/v2/carbon_project/users/login` | POST | JSON | 用户登录认证 |
| 用户管理 | `/api/carbon/v2/carbon_project/users/register` | POST | JSON | 用户注册 |
| 用户管理 | `/api/carbon/v2/carbon_project/users/info` | GET/PUT | JSON | 获取/更新用户信息 |
| 项目管理 | `/api/carbon/v2/carbon_project/users/projects` | GET/POST | JSON | 项目列表/创建项目 |
| 项目管理 | `/api/carbon/v2/carbon_project/users/projects/{id}` | GET/PUT/DELETE | JSON | 项目详情操作 |
| 计算结果 | `/api/carbon/v2/carbon_project/users/projects/{id}/result` | GET/POST | JSON | 获取/计算结果 |
| 清单管理 | `/api/carbon/v2/carbon_project/users/inventories` | GET/POST | JSON | 清单列表/创建清单 |
| 数据可视化 | `/api/carbon/v2/carbon_project/geojson` | GET | GeoJSON | 地理数据获取 |
| 统计分析 | `/api/carbon/v2/carbon_project/users/projects/overview` | GET | JSON | 项目概览统计 |
| 统计分析 | `/api/carbon/v2/carbon_project/users/projects/ranking` | GET | JSON | 项目排名数据 |

### 5.2 前端数据流架构

```mermaid
graph LR
    subgraph "Vue组件层"
        A[页面组件<br/>Views] --> B[业务组件<br/>Components]
        B --> C[基础组件<br/>Base Components]
    end

    subgraph "状态管理层"
        D[Vuex Store] --> E[模块化Store<br/>Modules]
        E --> F[Actions<br/>异步操作]
        E --> G[Mutations<br/>状态变更]
        E --> H[Getters<br/>计算属性]
    end

    subgraph "API服务层"
        I[API封装<br/>request.js] --> J[拦截器<br/>Interceptors]
        J --> K[错误处理<br/>Error Handler]
        J --> L[认证处理<br/>Auth Handler]
    end

    A --> D
    B --> D
    F --> I
    I --> M[后端API]

    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style I fill:#e8f5e8
```

### 5.3 后端服务架构

```mermaid
graph TB
    subgraph "控制器层 (Controllers)"
        A[RestController<br/>路由控制] --> B[认证验证<br/>Auth Check]
        B --> C[参数验证<br/>Param Validation]
    end

    subgraph "服务层 (Services)"
        C --> D[项目服务<br/>ProjectService]
        C --> E[清单服务<br/>InventoryService]
        C --> F[计算服务<br/>CalculationService]
        C --> G[用户服务<br/>UserService]
        C --> H[报表服务<br/>ReportService]
    end

    subgraph "模型层 (Models)"
        D --> I[carbon_project<br/>项目模型]
        E --> J[life_cycle_inventory<br/>清单模型]
        F --> K[carbon_project_result<br/>结果模型]
        G --> L[res_users<br/>用户模型]
        H --> M[project_report<br/>报表模型]
    end

    subgraph "数据层 (Database)"
        I --> N[(PostgreSQL)]
        J --> N
        K --> N
        L --> N
        M --> N
    end

    style A fill:#ffebee
    style D fill:#e8f5e8
    style I fill:#e3f2fd
    style N fill:#fff3e0
```

## 6. 数据封装分析

### 6.1 前端数据封装

#### 6.1.1 Vuex状态管理封装
- **模块化设计**: 每个业务模块有独立的store
- **状态集中管理**: 用户信息、项目数据、计算结果等全局状态
- **异步操作封装**: Actions处理API调用和数据转换

#### 6.1.2 API请求封装
```javascript
// request.js - 统一请求封装
requests.interceptors.request.use((config) => {
  const sessionID = localStorage.getItem('session')
  config.headers['X-Openerp-Session-Id'] = sessionID
  config.headers['Content-Type'] = 'application/json'
  return config
})
```

#### 6.1.3 组件数据封装
- **Props验证**: 组件间数据传递的类型检查
- **计算属性**: 复杂数据的派生和格式化
- **事件封装**: 统一的事件处理机制

### 6.2 后端数据封装

#### 6.2.1 数据模型封装
```python
# 使用Datamodel进行API数据验证
class ProjectDataModel(Datamodel):
    _name = "carbon.project.data"

    name = fields.String(required=True)
    area = fields.Float(required=True)
    life = fields.Integer(required=True)
```

#### 6.2.2 业务逻辑封装
- **服务层抽象**: 业务逻辑与数据访问分离
- **计算引擎封装**: 碳排放计算算法的模块化实现
- **权限控制封装**: 基于角色的访问控制

#### 6.2.3 数据访问封装
- **ORM映射**: Odoo ORM自动处理对象关系映射
- **事务管理**: 数据库操作的事务一致性保证
- **缓存机制**: 计算结果的缓存优化

## 7. 核心业务数据流程

### 7.1 项目创建到计算完整流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端Vue
    participant A as API网关
    participant S as 业务服务
    participant C as 计算引擎
    participant D as 数据库

    U->>F: 创建新项目
    F->>A: POST /users/projects
    A->>S: 项目创建服务
    S->>D: 保存项目基础信息
    D-->>S: 返回项目ID
    S-->>A: 项目创建成功
    A-->>F: 返回项目信息

    U->>F: 选择生命周期清单
    F->>A: GET /users/inventories
    A->>S: 清单查询服务
    S->>D: 查询可用清单
    D-->>S: 返回清单列表
    S-->>A: 清单数据
    A-->>F: 清单选项

    U->>F: 配置计算参数
    F->>A: PUT /users/projects/{id}
    A->>S: 项目更新服务
    S->>D: 更新项目配置

    U->>F: 执行碳排放计算
    F->>A: POST /users/projects/{id}/result
    A->>C: 启动计算引擎
    C->>D: 查询清单数据
    C->>C: 执行计算算法
    C->>D: 保存计算结果
    D-->>C: 确认保存
    C-->>A: 计算完成
    A-->>F: 返回计算结果
    F-->>U: 展示结果图表
```

### 7.2 数据计算引擎流程

```mermaid
flowchart TD
    A[接收计算请求] --> B{判断计算模式}
    B -->|粗算| C[粗算引擎]
    B -->|精算| D[精算引擎]

    C --> E[加载项目基础参数<br/>面积、年限、预算]
    D --> F[加载详细参数<br/>结构层配置、材料规格]

    E --> G[查询生命周期清单]
    F --> G

    G --> H[材料清单处理]
    G --> I[机械清单处理]
    G --> J[养护清单处理]
    G --> K[碳汇清单处理]

    H --> L[原材料阶段计算<br/>∑(用量 × 碳排放因子)]
    I --> M[运输阶段计算<br/>∑(距离 × 载重 × 因子)]
    I --> N[施工阶段计算<br/>∑(工时 × 功率 × 因子)]
    J --> O[养护阶段计算<br/>∑(周期 × 用量 × 因子)]
    K --> P[碳汇阶段计算<br/>∑(面积 × 年限 × 因子)]

    L --> Q[阶段结果汇总]
    M --> Q
    N --> Q
    O --> Q
    P --> Q

    Q --> R[生成多维度指标<br/>总量、强度、年均]
    R --> S[结果数据存储]
    S --> T[返回计算结果]

    style A fill:#e8f5e8
    style B fill:#fff3e0
    style Q fill:#f3e5f5
    style T fill:#e1f5fe
```

### 7.3 数据可视化流程

```mermaid
graph LR
    subgraph "数据源"
        A[计算结果数据] --> B[地理位置数据]
        A --> C[统计汇总数据]
        A --> D[时间序列数据]
    end

    subgraph "数据处理"
        B --> E[GeoJSON格式转换]
        C --> F[图表数据格式化]
        D --> G[时间轴数据处理]
    end

    subgraph "可视化组件"
        E --> H[Leaflet地图展示<br/>项目地理分布]
        F --> I[ECharts柱状图<br/>碳排放对比]
        F --> J[ECharts饼图<br/>阶段占比分析]
        G --> K[ECharts折线图<br/>趋势分析]
    end

    subgraph "交互功能"
        H --> L[地图缩放平移]
        I --> M[数据钻取]
        J --> N[图例筛选]
        K --> O[时间范围选择]
    end

    style A fill:#e3f2fd
    style E fill:#f1f8e9
    style H fill:#fce4ec
    style L fill:#fff3e0
```

## 8. 数据安全与性能优化

### 8.1 数据安全机制

#### 8.1.1 认证与授权
- **Session认证**: 基于X-Openerp-Session-Id的会话管理
- **权限控制**: 基于用户角色的数据访问控制
- **CSRF保护**: 跨站请求伪造防护

#### 8.1.2 数据传输安全
- **HTTPS加密**: 生产环境强制HTTPS传输
- **CORS配置**: 跨域资源共享安全配置
- **数据验证**: 前后端双重数据格式验证

### 8.2 性能优化策略

#### 8.2.1 前端优化
- **路由懒加载**: 按需加载页面组件
- **组件缓存**: keep-alive缓存频繁访问组件
- **图表优化**: ECharts按需引入和数据采样

#### 8.2.2 后端优化
- **数据库索引**: 关键查询字段建立索引
- **计算结果缓存**: 避免重复计算
- **分页查询**: 大数据量分页处理

#### 8.2.3 网络优化
- **代理缓存**: 静态资源缓存策略
- **数据压缩**: Gzip压缩减少传输量
- **CDN加速**: 静态资源CDN分发

## 9. 总结

### 9.1 数据流程特点

1. **分层架构**: 清晰的前后端分离和分层设计
2. **模块化**: 业务模块独立，便于维护和扩展
3. **标准化**: RESTful API和统一的数据格式
4. **可视化**: 丰富的图表和地图展示能力

### 9.2 技术优势

1. **成熟技术栈**: Vue.js + Odoo双重保障
2. **灵活计算**: 支持多种计算模式和自定义配置
3. **数据驱动**: 基于科学的生命周期清单数据
4. **用户友好**: 直观的操作界面和交互体验

### 9.3 扩展性分析

1. **水平扩展**: 支持多实例部署和负载均衡
2. **功能扩展**: 模块化设计便于新功能集成
3. **数据扩展**: 灵活的数据模型支持业务扩展
4. **接口扩展**: 标准化API便于第三方集成

---

*本文档版本: v1.0*
*最后更新: 2025-07-24*
*文档作者: 系统分析*
