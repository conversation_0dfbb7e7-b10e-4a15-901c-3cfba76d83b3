# 10.1.3 功能需求分析

## 1. 功能需求概述

道路工程能耗与碳排放LCA软件的功能需求分析基于对用户需求的深入理解和系统业务流程的梳理，结合当前项目的技术架构和实现方案，将系统功能需求划分为核心业务功能、支撑管理功能和扩展服务功能三个层次。

### 1.1 功能需求分类框架

```mermaid
graph TB
    A[功能需求] --> B[核心业务功能]
    A --> C[支撑管理功能]
    A --> D[扩展服务功能]
    
    B --> B1[用户认证与权限管理]
    B --> B2[项目管理]
    B --> B3[数据采集与管理]
    B --> B4[碳排放计算]
    B --> B5[结果分析与展示]
    B --> B6[报表生成]
    
    C --> C1[基础数据管理]
    C --> C2[系统配置管理]
    C --> C3[日志与监控]
    C --> C4[数据备份与恢复]
    
    D --> D1[API接口服务]
    D --> D2[第三方集成]
    D --> D3[移动端支持]
    D --> D4[智能分析]
```

### 1.2 功能优先级定义

- **P0（核心功能）**：系统基本运行必需的功能，缺失将导致系统无法使用
- **P1（重要功能）**：显著提升用户体验和工作效率的功能
- **P2（一般功能）**：增强系统易用性和完整性的功能
- **P3（扩展功能）**：面向未来发展和特殊需求的功能

## 2. 核心业务功能需求

### 2.1 用户认证与权限管理功能

#### 2.1.1 用户认证功能（P0）

**功能描述：** 提供安全可靠的用户身份验证机制，确保系统访问安全。

**具体需求：**
- **多种登录方式**
  - 用户名密码登录
  - 手机号验证码登录
  - 第三方认证登录（预留接口）
- **会话管理**
  - 基于Session的会话控制
  - 自动登录状态检测
  - 会话超时自动退出
- **安全机制**
  - 密码强度验证
  - 登录失败次数限制
  - 验证码防护机制

**技术实现：**
- 前端：Vue Router路由守卫、localStorage会话存储
- 后端：Odoo Session认证、用户角色权限控制
- 安全：HTTPS传输、密码加密存储

**验收标准：**
- 支持多种登录方式，登录成功率≥99%
- 会话管理稳定，无异常退出
- 安全机制有效，防止暴力破解

#### 2.1.2 权限管理功能（P0）

**功能描述：** 实现基于角色的权限控制，确保用户只能访问授权的功能和数据。

**具体需求：**
- **角色定义**
  - 系统管理员：完整系统管理权限
  - 项目经理：项目创建和管理权限
  - 数据录入员：数据录入和编辑权限
  - 分析师：计算执行和结果分析权限
  - 查看用户：只读查看权限
- **权限控制**
  - 功能模块访问控制
  - 数据操作权限控制
  - API接口访问控制
- **数据隔离**
  - 用户只能访问自己创建的项目
  - 主子账号数据共享机制
  - 跨用户数据访问授权

**技术实现：**
- 基于Odoo的用户角色权限体系
- 前端路由级权限控制
- 后端API接口权限验证

### 2.2 项目管理功能

#### 2.2.1 项目创建与配置功能（P0）

**功能描述：** 提供完整的项目生命周期管理，从项目创建到配置完成的全流程支持。

**具体需求：**
- **项目基本信息管理**
  - 项目名称、编号、描述
  - 项目地理位置（省市区选择）
  - 道路等级、铺装类型
  - 铺装面积、设计使用年限
- **计算模式选择**
  - 粗算模式：快速估算，适用于方案比选
  - 精算模式：详细计算，适用于精确评估
- **生命周期阶段配置**
  - 原材料生产阶段
  - 材料运输阶段
  - 施工建造阶段
  - 运营维护阶段
  - 拆除处置阶段
  - 碳汇效应阶段
- **项目状态管理**
  - 创建中、配置中、计算中、已完成
  - 状态流转控制和权限验证

**数据模型：**
```python
# 项目主表 (carbon_project)
- id: 项目唯一标识
- name: 项目名称
- location: 项目地点
- area: 铺装面积
- life: 使用年限
- type: 铺装类型
- mode: 计算模式 (rough/fine)
- stage_ids: 关联的生命周期阶段
- user_id: 所属用户
- is_completed: 是否完成计算
```

#### 2.2.2 项目方案管理功能（P1）

**功能描述：** 支持一个项目下多个计算方案的管理，便于方案对比和优化。

**具体需求：**
- **方案创建与编辑**
  - 基于项目创建多个计算方案
  - 方案参数独立配置
  - 方案复制和模板功能
- **方案状态管理**
  - 方案激活状态控制
  - 计算完成状态跟踪
  - 方案版本管理
- **方案对比功能**
  - 多方案并行对比
  - 差异化分析展示
  - 优劣势对比报告

### 2.3 数据采集与管理功能

#### 2.3.1 工程量数据采集功能（P0）

**功能描述：** 提供结构化的工程量数据采集界面，支持多种数据录入方式。

**具体需求：**
- **数据录入界面**
  - 分阶段的数据录入表单
  - 材料用量、设备使用、能源消耗数据
  - 实时数据验证和提示
- **数据录入方式**
  - 手动逐项录入
  - 批量数据导入（Excel、CSV）
  - 从BIM模型导入（预留接口）
- **数据验证机制**
  - 数值范围合理性检查
  - 单位一致性验证
  - 必填项完整性检查
  - 异常值标记和提醒

**技术实现：**
- Vue.js动态表单组件
- Element UI表单验证
- 文件上传和解析功能

#### 2.3.2 生命周期清单管理功能（P0）

**功能描述：** 管理道路工程生命周期评价所需的基础清单数据。

**具体需求：**
- **清单类型管理**
  - 材料生命周期清单
  - 机械设备清单
  - 养护作业清单
  - 碳汇清单
- **清单数据维护**
  - 清单项目的增删改查
  - 碳排放因子配置
  - 单位换算关系
  - 清单版本管理
- **清单应用管理**
  - 清单与项目的关联
  - 清单激活状态控制
  - 清单使用统计

### 2.4 碳排放计算功能

#### 2.4.1 计算引擎功能（P0）

**功能描述：** 系统的核心功能，实现道路工程全生命周期碳排放的精确计算。

**具体需求：**
- **计算模式支持**
  - 粗算模式：基于典型参数的快速计算
  - 精算模式：基于详细数据的精确计算
- **分阶段计算**
  - 原材料阶段：材料生产的内含碳排放
  - 运输阶段：材料和设备运输的碳排放
  - 施工阶段：施工机械和能源消耗的碳排放
  - 养护阶段：运营期维护作业的碳排放
  - 拆除阶段：拆除处置过程的碳排放
  - 碳汇阶段：绿化等碳汇效应的计算
- **计算方法**
  - 基于活动数据×排放因子的计算方法
  - 符合ISO 14040/14044标准
  - 支持多种温室气体的CO₂当量换算

**计算公式：**
```
总碳排放 = Σ(各阶段碳排放量)
单位面积碳排放强度 = 总碳排放 / 铺装面积
年均碳排放强度 = 总碳排放 / 使用年限
单位面积年均碳排放强度 = 单位面积碳排放强度 / 使用年限
```

**技术实现：**
```python
# 计算核心逻辑 (carbon_project_scheme.calc_rough)
def calc_rough(self):
    # 获取项目基础数据
    data = json.loads(self.data)
    A_Area = float(self.project_id.area)
    A_Year = float(data.get('A-Year'))
    
    # 分阶段计算
    stage_data = {}
    for stage in self.project_id.stage_ids:
        stage_data[stage.name] = []
    
    # 材料碳排放计算
    # 运输碳排放计算
    # 施工碳排放计算
    # 养护碳排放计算
    # 碳汇效应计算
    
    # 结果汇总和存储
    for stage_name, values in stage_data.items():
        result = CarbonProjectResult.create({
            'scheme_id': self.id,
            'stage_id': stage_id,
            'res_all': sum(values) * A_Area / 1000,
            'res_area': sum(values),
            'res_year': sum(values) * A_Area / (A_Year * 1000),
            'res_area_year': sum(values) / A_Year
        })
```

#### 2.4.2 计算结果管理功能（P0）

**功能描述：** 管理和存储计算结果，提供结果查询和分析功能。

**具体需求：**
- **结果存储**
  - 分阶段结果存储
  - 分类别结果存储
  - 计算过程数据保存
- **结果查询**
  - 按项目查询结果
  - 按阶段查询结果
  - 按时间范围查询
- **结果验证**
  - 计算结果合理性检查
  - 与历史数据对比验证
  - 异常结果标记

### 2.5 结果分析与展示功能

#### 2.5.1 数据可视化功能（P1）

**功能描述：** 通过图表、地图等可视化方式展示计算结果和分析数据。

**具体需求：**
- **图表展示**
  - 饼图：各阶段碳排放占比
  - 柱状图：不同项目碳排放对比
  - 折线图：时间序列变化趋势
  - 桑基图：碳排放流向分析
- **地图展示**
  - 项目地理分布展示
  - 区域碳排放热力图
  - 交互式地图操作
- **仪表盘**
  - 项目概览仪表盘
  - 实时数据监控
  - 关键指标展示

**技术实现：**
- ECharts图表库
- Leaflet地图组件
- Vue-ECharts集成

#### 2.5.2 结果分析功能（P1）

**功能描述：** 提供深入的结果分析工具，帮助用户理解碳排放构成和影响因素。

**具体需求：**
- **热点分析**
  - 自动识别高排放环节
  - 排放贡献度排序
  - 关键影响因素分析
- **对比分析**
  - 多项目横向对比
  - 多方案纵向对比
  - 与行业基准对比
- **趋势分析**
  - 历史数据趋势分析
  - 预测分析功能
  - 敏感性分析

### 2.6 报表生成功能

#### 2.6.1 报表模板管理功能（P1）

**功能描述：** 提供标准化的报表模板，支持自动生成专业报告。

**具体需求：**
- **报表类型**
  - 项目概览报表
  - 详细计算报表
  - 方案对比报表
  - 阶段分析报表
- **模板管理**
  - 预设报表模板
  - 自定义模板功能
  - 模板版本管理
- **报表内容**
  - 项目基本信息
  - 计算方法说明
  - 详细计算数据
  - 结果分析图表
  - 结论和建议

#### 2.6.2 报表生成与导出功能（P1）

**功能描述：** 基于计算结果自动生成报表，支持多种格式导出。

**具体需求：**
- **自动生成**
  - 基于模板自动生成报表
  - 数据自动填充
  - 图表自动嵌入
- **格式支持**
  - PDF格式导出
  - Word格式导出
  - Excel格式导出
  - HTML在线查看
- **报表管理**
  - 报表版本控制
  - 报表共享功能
  - 报表下载记录

**技术实现：**
- ReportLab PDF生成
- Python-docx Word生成
- 后端报表服务

## 3. 支撑管理功能需求

### 3.1 基础数据管理功能

#### 3.1.1 系统基础数据维护（P1）

**功能描述：** 维护系统运行所需的基础数据和配置信息。

**具体需求：**
- **行政区划管理**
  - 省市区三级行政区划
  - 区划代码和名称维护
  - 地理坐标信息
- **单位管理**
  - 计量单位定义
  - 单位换算关系
  - 单位分类管理
- **阶段管理**
  - 生命周期阶段定义
  - 阶段顺序和关系
  - 阶段参数配置

#### 3.1.2 碳排放因子数据库（P0）

**功能描述：** 维护权威的碳排放因子数据库，确保计算结果的准确性。

**具体需求：**
- **因子数据管理**
  - 材料碳排放因子
  - 能源碳排放因子
  - 设备碳排放因子
- **数据来源管理**
  - 数据来源标注
  - 数据更新时间
  - 数据可靠性等级
- **版本管理**
  - 因子数据版本控制
  - 历史版本查询
  - 版本切换功能

### 3.2 系统配置管理功能

#### 3.2.1 系统参数配置（P2）

**功能描述：** 提供系统运行参数的配置和管理功能。

**具体需求：**
- **计算参数配置**
  - 默认计算参数设置
  - 计算精度配置
  - 计算边界设置
- **界面配置**
  - 系统主题设置
  - 语言切换配置
  - 显示格式设置
- **业务规则配置**
  - 数据验证规则
  - 业务流程配置
  - 权限规则设置

### 3.3 日志与监控功能

#### 3.3.1 操作日志管理（P2）

**功能描述：** 记录用户操作和系统运行日志，支持审计和问题排查。

**具体需求：**
- **用户操作日志**
  - 登录登出记录
  - 数据操作记录
  - 计算执行记录
- **系统运行日志**
  - 系统启动停止
  - 错误异常记录
  - 性能监控数据
- **日志查询分析**
  - 日志检索功能
  - 日志统计分析
  - 异常告警机制

## 4. 扩展服务功能需求

### 4.1 API接口服务功能

#### 4.1.1 RESTful API接口（P2）

**功能描述：** 提供标准化的API接口，支持第三方系统集成。

**具体需求：**
- **数据接口**
  - 项目数据查询接口
  - 计算结果查询接口
  - 基础数据查询接口
- **功能接口**
  - 计算执行接口
  - 报表生成接口
  - 用户认证接口
- **接口管理**
  - API文档自动生成
  - 接口版本管理
  - 接口访问控制

### 4.2 第三方集成功能

#### 4.2.1 BIM系统集成（P3）

**功能描述：** 与主流BIM软件集成，实现工程量数据的自动导入。

**具体需求：**
- **数据导入**
  - BIM模型数据解析
  - 工程量自动提取
  - 数据格式转换
- **双向同步**
  - 数据变更同步
  - 结果反馈到BIM
  - 版本一致性控制

### 4.3 移动端支持功能

#### 4.3.1 移动端数据采集（P3）

**功能描述：** 提供移动端应用，支持现场数据采集和查看。

**具体需求：**
- **现场数据录入**
  - 移动端表单录入
  - 照片关联功能
  - 离线数据缓存
- **结果查看**
  - 移动端结果展示
  - 简化版图表显示
  - 报表移动端查看

### 4.4 智能分析功能

#### 4.4.1 智能优化建议（P3）

**功能描述：** 基于机器学习算法提供智能化的优化建议。

**具体需求：**
- **参数推荐**
  - 基于历史数据的参数推荐
  - 相似项目参数建议
  - 最优参数组合推荐
- **优化建议**
  - 减排措施建议
  - 材料替代建议
  - 工艺优化建议

## 5. 功能需求总结

### 5.1 功能需求统计

| 功能类别 | P0核心功能 | P1重要功能 | P2一般功能 | P3扩展功能 | 合计 |
|---------|-----------|-----------|-----------|-----------|------|
| 核心业务功能 | 8 | 6 | 2 | 0 | 16 |
| 支撑管理功能 | 1 | 2 | 3 | 0 | 6 |
| 扩展服务功能 | 0 | 0 | 1 | 4 | 5 |
| **合计** | **9** | **8** | **6** | **4** | **27** |

### 5.2 开发优先级建议

**第一阶段（MVP版本）**：实现所有P0核心功能
- 用户认证与权限管理
- 项目创建与配置
- 数据采集与管理
- 碳排放计算引擎
- 基础结果展示
- 碳排放因子数据库

**第二阶段（完整版本）**：实现P1重要功能
- 数据可视化
- 结果分析
- 报表生成
- 项目方案管理
- 基础数据管理

**第三阶段（增强版本）**：实现P2一般功能和P3扩展功能
- 系统配置管理
- 日志监控
- API接口
- 第三方集成
- 移动端支持
- 智能分析

### 5.3 关键成功因素

1. **计算准确性**：确保碳排放计算结果的科学性和准确性
2. **用户体验**：提供直观易用的用户界面和操作流程
3. **系统性能**：保证系统响应速度和并发处理能力
4. **数据安全**：确保用户数据的安全性和隐私保护
5. **扩展性**：支持功能扩展和第三方系统集成

通过系统化的功能需求分析，为道路工程能耗与碳排放LCA软件的设计和开发提供了清晰的功能蓝图和实施路径。
