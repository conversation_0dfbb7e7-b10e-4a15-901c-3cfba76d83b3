# 路面生命周期碳排放计算管理平台 - 软件总体使用流程文档

## 1. 系统概述

路面生命周期碳排放计算管理平台是一个专门用于计算和管理道路铺面全生命周期碳排放的Web应用系统。该平台采用前后端分离架构，为用户提供从项目创建到碳排放计算结果分析的完整解决方案。

## 2. 技术架构

### 2.1 前端技术栈
- **框架**: Vue.js 2.6.14
- **UI组件库**: Element UI 2.15.14
- **状态管理**: Vuex 3.6.2
- **路由管理**: Vue Router 3.5.1
- **图表库**: ECharts 4.9.0 + Vue-ECharts 5.0.0
- **地图组件**: Leaflet 1.7.1
- **HTTP客户端**: Axios 1.6.1
- **样式预处理**: Less 4.0.0

### 2.2 后端技术栈
- **框架**: Odoo 12
- **编程语言**: Python
- **数据库**: PostgreSQL
- **Web服务器**: uWSGI 2.0.19.1
- **API框架**: base_rest (Odoo REST API扩展)
- **报告生成**: ReportLab + PyPDF2
- **数据处理**: Matplotlib 3.3.4

## 3. 系统架构图

```mermaid
graph TB
    A[用户浏览器] --> B[Vue.js前端应用]
    B --> C[Nginx反向代理]
    C --> D[Odoo后端服务]
    D --> E[PostgreSQL数据库]
    D --> F[文件存储系统]
    
    subgraph "前端模块"
        B1[用户管理模块]
        B2[项目管理模块]
        B3[清单管理模块]
        B4[结果展示模块]
    end
    
    subgraph "后端模块"
        D1[认证服务]
        D2[项目服务]
        D3[计算引擎]
        D4[报告生成]
    end
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
```

## 4. 主要功能模块

### 4.1 用户管理模块
- 用户注册和登录
- 权限控制和会话管理
- 用户信息维护

### 4.2 项目管理模块
- 项目创建和基本信息配置
- 项目方案管理（粗略模式/精细模式）
- 项目数据填报和维护

### 4.3 生命周期清单管理模块
- 材料清单管理
- 机械设备清单管理
- 养护清单管理
- 清单数据的增删改查

### 4.4 碳排放计算模块
- 基于生命周期评价(LCA)方法的计算引擎
- 支持粗略模式和精细模式计算
- 多阶段碳排放计算

### 4.5 结果展示和报告模块
- 计算结果可视化展示
- 多维度数据分析
- PDF报告生成和下载

## 5. 系统输入输出

### 5.1 输入内容

#### 5.1.1 项目基本信息
- **项目名称**: 用于标识和管理项目
- **项目地点**: 影响运输距离和环境因子
- **铺装面积**: 计算基础数据（平方米）
- **使用年限**: 生命周期时间范围（年）
- **铺装类型**: 影响材料和工艺选择

#### 5.1.2 计算模式选择
- **粗略模式**: 基于经验数据的快速估算
- **精细模式**: 基于详细清单的精确计算

#### 5.1.3 生命周期清单数据
- **材料清单**: 各类建筑材料的碳排放因子
- **运输机械清单**: 运输设备的能耗和排放数据
- **施工机械清单**: 施工设备的能耗和排放数据
- **拆除机械清单**: 拆除设备的能耗和排放数据
- **养护清单**: 维护保养的材料和能耗数据

### 5.2 输出内容

#### 5.2.1 计算结果指标
- **总碳排放量**: 项目全生命周期总碳排放（tCO₂e）
- **单位面积碳排放强度**: 每平方米碳排放量（tCO₂e/m²）
- **平均每年碳排放强度**: 年均碳排放量（tCO₂e/年）
- **单位面积年均碳排放强度**: 每平方米年均碳排放（tCO₂e/m²/年）

#### 5.2.2 详细分析结果
- **各阶段碳排放分解**: 材料生产、运输、施工、使用、拆除等阶段
- **可视化图表**: 柱状图、饼图、趋势图等
- **对比分析**: 不同方案的对比结果

#### 5.2.3 报告文档
- **PDF格式报告**: 包含完整计算过程和结果
- **数据导出**: 支持Excel等格式的数据导出

## 6. 详细使用流程

### 6.1 整体业务流程图

```mermaid
flowchart TD
    A[用户访问系统] --> B{是否已登录}
    B -->|否| C[用户登录/注册]
    B -->|是| D[进入主界面]
    C --> D
    
    D --> E[选择功能模块]
    E --> F{功能选择}
    
    F -->|项目管理| G[项目管理流程]
    F -->|清单管理| H[清单管理流程]
    F -->|结果查看| I[结果查看流程]
    
    G --> J[创建新项目]
    J --> K[填写项目基本信息]
    K --> L[选择计算模式]
    L --> M[配置项目方案]
    M --> N[提交计算]
    N --> O[查看计算结果]
    
    H --> P[创建/编辑清单]
    P --> Q[配置清单数据]
    Q --> R[激活清单]
    
    I --> S[选择项目]
    S --> T[查看计算结果]
    T --> U[生成报告]
    U --> V[下载报告]
    
    O --> W[流程结束]
    R --> W
    V --> W
```

### 6.2 项目管理详细流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端界面
    participant B as 后端API
    participant D as 数据库
    participant C as 计算引擎
    
    U->>F: 创建新项目
    F->>U: 显示项目信息表单
    U->>F: 填写项目基本信息
    F->>B: 提交项目数据
    B->>D: 保存项目信息
    D-->>B: 返回项目ID
    B-->>F: 返回创建结果
    
    U->>F: 选择计算模式
    F->>U: 显示方案配置界面
    U->>F: 配置项目方案
    F->>B: 提交方案数据
    B->>D: 保存方案配置
    
    U->>F: 启动计算
    F->>B: 发起计算请求
    B->>C: 调用计算引擎
    C->>D: 读取清单数据
    C->>C: 执行碳排放计算
    C->>D: 保存计算结果
    C-->>B: 返回计算完成
    B-->>F: 返回计算状态
    F-->>U: 显示计算完成
```

## 7. 中间操作详解

### 7.1 用户认证和权限管理
- **会话管理**: 基于localStorage的前端会话存储
- **路由守卫**: 自动检查登录状态，未登录用户重定向到登录页
- **权限控制**: 基于用户角色的功能权限控制

### 7.2 数据流转过程
1. **前端数据收集**: Vue组件收集用户输入数据
2. **数据验证**: 前端表单验证 + 后端数据校验
3. **API调用**: Axios发送HTTP请求到Odoo REST API
4. **业务逻辑处理**: Odoo模型层处理业务逻辑
5. **数据持久化**: PostgreSQL数据库存储
6. **结果返回**: 处理结果返回前端展示

### 7.3 计算引擎工作流程
1. **数据准备**: 从数据库读取项目信息和清单数据
2. **参数配置**: 根据项目模式和方案配置计算参数
3. **分阶段计算**: 按照生命周期阶段进行碳排放计算
4. **结果汇总**: 汇总各阶段结果，计算总体指标
5. **结果存储**: 将计算结果保存到数据库

## 8. 系统特点

### 8.1 技术特点
- **前后端分离**: 提高开发效率和系统可维护性
- **模块化设计**: 功能模块独立，便于扩展和维护
- **响应式设计**: 支持多种设备访问
- **数据可视化**: 丰富的图表展示功能

### 8.2 业务特点
- **专业性强**: 基于LCA方法的专业碳排放计算
- **灵活配置**: 支持自定义清单和计算参数
- **多模式支持**: 粗略模式和精细模式满足不同需求
- **结果丰富**: 多维度的计算结果和分析报告

## 9. 部署架构

```mermaid
graph LR
    A[负载均衡器] --> B[Web服务器集群]
    B --> C[应用服务器集群]
    C --> D[数据库集群]
    
    subgraph "Web层"
        B1[Nginx 1]
        B2[Nginx 2]
    end
    
    subgraph "应用层"
        C1[Odoo实例1]
        C2[Odoo实例2]
    end
    
    subgraph "数据层"
        D1[PostgreSQL主库]
        D2[PostgreSQL从库]
    end
    
    B --> B1
    B --> B2
    C --> C1
    C --> C2
    D --> D1
    D --> D2
```

## 10. 总结

路面生命周期碳排放计算管理平台通过现代化的Web技术栈，为道路建设行业提供了专业的碳排放计算和管理解决方案。系统具有良好的用户体验、强大的计算能力和丰富的结果展示功能，能够满足不同层次用户的需求，为推动绿色交通建设提供有力的技术支撑。
